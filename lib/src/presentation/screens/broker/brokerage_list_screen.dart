import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../domain/models/broker_api.dart';
import '../../../domain/models/user.dart';
import '../../cubit/broker/broker_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/tables/action_button_eye.dart';
import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';

class BrokerageListScreen extends HookWidget {
  const BrokerageListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Call the method to read and print sales data from JSON
    final sortedBrokers = useState<List<Brokers>>([]);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final totalBrokers = useState(0);
    final ValueNotifier<String?> searchString = useState(null);
    final ValueNotifier<DateTime?> selectedDate = useState(null);

    final user = context.watch<UserCubit>().state.user;

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListStateColumnHeader,
      brokerListCityColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
      brokerListTotalRevenueColumnHeader,
      brokerListCommissionColumnHeader,
    ];

    void handleSort(String columnName, bool ascending) {
      // TODO: API
    }

    Future<void> _fetchBrokers(
      BuildContext context,
      User? user, {
      required DateTime? selectedDate,
      required int page,
      required String? searchString,
    }) async {
      String? formattedDate = selectedDate != null
          ? AppDateFormatter.formatDateMMddyyyy(selectedDate)
          : null;

      // TODO: update sortBy static value when server side updates
      final payload = {
        "page": page > 0 ? page - 1 : 0,
        "size": 10,
        "sortBy": "id",
        "sortDirection": "ASC",
        "searchString": searchString,
        "joiningDate": formattedDate,
        "userId": user?.userId,
      };
      await context.read<BrokerCubit>().fetchBrokers(payload);
    }

    useEffect(() {
      //create microtask async
      Future.microtask(() async {
        if (context.mounted) {
          // final BrokerCubit brokerCubit = context.read<BrokerCubit>();
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
          );
        }
      });

      return null;
    }, []);

    return BlocConsumer<BrokerCubit, BrokerState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        if (state is BrokerLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pageCount.value = state.brokerApi?.totalPages ?? 0;
            sortedBrokers.value = state.brokerApi?.brokers ?? [];
            totalBrokers.value = state.brokerApi?.totalElements ?? 0;
          });
        }

        return CustomDataTableWidget<Brokers>(
          data: sortedBrokers.value,
          title: brokersTab,
          titleIcon: "$iconAssetpath/user.png",
          searchHint: searchHint,
          searchFn: (broker) =>
              broker.fullName +
              broker.phone +
              broker.email +
              broker.city +
              broker.state +
              AppDateFormatter.formatJoiningDate(broker.joiningDate) +
              broker.totalDownlineAgents.toString() +
              broker.totalSales.toString() +
              broker.totalRevenue.toString(),
          // Dynamic filtering system
          filterColumnNames: [
            brokerListJoinDateColumnHeader, // Join date filter
          ],
          filterValueExtractors: {
            brokerListJoinDateColumnHeader: (broker) {
              return AppDateFormatter.formatJoiningDate(broker.joiningDate);
            },
          },
          // Date filter configuration - specify which columns should use date filters
          dateFilterColumns: const [
            brokerListJoinDateColumnHeader, // Join date should use calendar picker
          ],
          columnNames: formattedHeaders,
          cellBuilders: [
            (broker) => broker.fullName,
            (broker) => broker.phone,
            (broker) => broker.email,
            (broker) => AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
            (broker) => broker.state,
            (broker) => broker.city,
            (broker) => broker.totalDownlineAgents.toString(),
            (broker) => '${broker.totalSales}',
            (broker) => '\$${broker.totalRevenue}',
            (broker) => '\$${broker.commission}',
          ],
          iconCellBuilders: [
            (broker) => TableCellData(
              text: broker.fullName,
              leftIconAsset: "$iconAssetpath/agent_round.png",
              iconSize: 30,
            ),
            null, // contact - no icon
            null, // email - no icon
            null, // address - no icon
            null, // joinDate - no icon
            null, // agents - no icon
            null, // totalSales - no icon
            null, // totalrevenue - no icon
            null, // commision- no icon
            null, // city - no icon
          ],
          useIconBuilders: [
            true, // name - use icon
            false, // contact - use text
            false, // email - use text
            false, // address - use text
            false, // joinDate - use text
            false, // agents - use text
            false, // totalSales - use text
            false, // totalrevenue - use text
            false, // commision - use text
            false, // city - use text
          ],
          actionBuilders: [
            (context, broker) => ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isCompact: true,
              isMobile: false,
            ),
          ],

          mobileCardBuilder: (context, broker) =>
              _buildMobileBrokerCard(broker, context),
          onSort: handleSort,
          emptyStateMessage: noDataAvailable,
          pageCount: pageCount.value,
          totalElements: totalBrokers.value,
          isLoading: state is BrokerLoading,
          useExpandedView: false,
          onDateFilterChanged: (value) async {
            selectedDate.value = value;
            await _fetchBrokers(
              context,
              user,
              selectedDate: value,
              page: currentpage.value,
              searchString: searchString.value,
            );
          },
          handleTableSearch: (value) async {
            searchString.value = value;
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              page: currentpage.value,
              searchString: value,
            );
          },
          handlePagination: (page) async {
            currentpage.value = page;
            await _fetchBrokers(
              context,
              user,
              selectedDate: selectedDate.value,
              searchString: searchString.value,
              page: page,
            );
          },
        );
      },
    );
  }

  void _onBrokerAction(BuildContext context, Brokers broker) {
    // Navigate to sales detail or show action
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Action clicked for ${broker.fullName}')),
    );
  }

  Widget _buildMobileBrokerCard(Brokers broker, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                broker.fullName,
                style: AppFonts.semiBoldTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$brokerListContactColumnHeader: ${broker.phone}'),
          Text('$brokerListEmailColumnHeader: ${broker.email}'),
          Text('$brokerListCityColumnHeader: ${broker.city}'),
          Text('$brokerListStateColumnHeader: ${broker.state}'),
          Text(
            '$brokerListJoinDateColumnHeader: ${AppDateFormatter.formatJoiningDate(broker.joiningDate)}',
          ),

          Text('$brokerListAgentsColumnHeader: ${broker.totalDownlineAgents}'),
          Text('$brokerListTotalSalesColumnHeader: ${broker.totalSales}'),
          Text(
            '$brokerListTotalRevenueColumnHeader: \$${broker.totalRevenue.toStringAsFixed(2)}',
          ),
          Text(
            '$brokerListCommissionColumnHeader: \$${broker.commission.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
